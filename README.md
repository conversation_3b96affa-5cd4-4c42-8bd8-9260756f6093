# TPA API

A FastAPI-based REST API for insurance policy and claims management with mock data.

## Overview

This API provides three endpoints for Third Party Administrator (TPA) operations:

1. **PolicyListSF** - Search and list insurance policies
2. **PolicyDetailSF** - Get detailed policy information including benefits and conditions
3. **ClaimListSF** - List claims history for members

## Features

- ✅ FastAPI with automatic request/response validation
- ✅ Comprehensive mock data in CSV format
- ✅ In-memory data storage for fast responses
- ✅ Complex parameter validation for policy searches
- ✅ Support for Thai and English content
- ✅ Docker containerization ready
- ✅ Structured logging
- ✅ Error handling and validation

## API Endpoints

### 1. PolicyListSF
```
GET /api/PolicyListSF
```
Search policies using various parameter combinations.

### 2. PolicyDetailSF
```
GET /api/PolicyDetailSF?MEMBER_CODE={member_code}
```
Get detailed policy information for a specific member.

### 3. ClaimListSF
```
GET /api/ClaimListSF
```
List claims using member code or insurer code + citizen ID.

## Quick Start

### Local Development

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the API:**
   ```bash
   uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
   ```

3. **Access the API:**
   - API Base URL: http://localhost:8000
   - Health Check: http://localhost:8000/health

### Docker Deployment

```bash
# Build the container
docker build -f docker/Dockerfile -t tpa-api .

# Run the container
docker run -p 8000:8000 tpa-api
```

## Project Structure

```
tpa-api/
├── src/                    # Main application code
│   ├── api/               # API routes and dependencies
│   ├── models/            # Pydantic models
│   ├── services/          # Business logic
│   ├── data/              # In-memory data management
│   └── utils/             # Utilities and helpers
├── data/csv/              # Mock data in CSV format
├── config/                # Configuration
├── docker/                # Docker configuration
└── requirements.txt       # Python dependencies
```

## Mock Data

The API uses comprehensive mock data stored in CSV files:

- **12 members** with various statuses and types
- **13 claims** covering all required statuses
- **Multiple insurance companies** and plans
- **Complex benefit structures** with limits and balances
- **Contract and member conditions**
- **Payment methods** and details

See `data/csv/README.md` for detailed information about the mock data structure.

## Development

This project follows Python best practices:

- **FastAPI** for modern, fast API development
- **Pydantic** for data validation and serialization
- **Structured logging** for observability
- **Clean architecture** with separation of concerns
- **Type hints** throughout the codebase

## License

This project is for demonstration purposes.
