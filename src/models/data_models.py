"""
Pydantic models for internal data structures.
Defines data models that map to CSV file structures for type safety and validation.
"""

from datetime import datetime, date
from typing import Optional, List
from pydantic import BaseModel, Field, validator
from src.utils.helpers import parse_date, parse_datetime


class PolicyRecord(BaseModel):
    """Model for policy data from policies.csv."""
    
    MemberCode: str
    MemberStatus: str
    TitleTH: str
    NameTH: str
    SurnameTH: str
    TitleEN: str
    NameEN: str
    SurnameEN: str
    CitizenID: str
    OtherID: str
    StaffNo: str
    InsurerCardNo: str
    InsPreviousCardNo: str
    PolicyNo: str
    CertificateNo: str
    MemberType: str
    PrincipleMemberCode: str
    PrincipleName: str
    VIP: str
    VIPRemarks: str
    CardType: str
    Language: str
    InsurerCode: str
    InsurerName: str
    InsurerNameEN: str
    CompanyCode: str
    CompanyName: str
    CompanyNameEN: str
    BirthDate: str
    Gender: str
    Citizenship: str
    CountryCode: str
    PlanCode: str
    PlanName: str
    PlanEffFrom: str
    PlanEffTo: str
    Mobile: str
    Email: str
    
    @validator('BirthDate', 'PlanEffFrom', 'PlanEffTo')
    def validate_dates(cls, v, field):
        """Validate date fields."""
        if v:
            try:
                parse_date(v, field.name)
            except Exception:
                pass  # Keep original string, validation happens in helpers
        return v


class PaymentDetail(BaseModel):
    """Model for payment details from payment_details.csv."""
    
    MemberCode: str
    PaymentMethod: str
    PayeeName: str
    BankAccNo: str
    BankName: str
    Primary: str


class PolicyDetail(BaseModel):
    """Model for policy details from policy_details.csv."""
    
    MemberCode: str
    MainBenefitCode: str
    MainBenefit: str
    MainBenefitEN: str
    MainPlanLimitDesc: str
    MainPlanAmount: str
    MainPlanUnit1: str
    MainPlanUnit2: str
    MainPlanBalance: str


class BenefitRecord(BaseModel):
    """Model for benefits from benefits.csv."""
    
    MemberCode: str
    BenefitCode: str
    BenefitTH: str
    BenefitEN: str
    SubBenefitCode: str
    SubBenefitTH: str
    SubBenefitEN: str
    LimitCode: str
    LimitAmt: str
    LimitUnit: str
    BalanceCode: str
    BalanceLimitAmt: str
    BalanceUnit: str
    ComSubLimitCode: str
    ComSubLimitAmt: str
    ComSubLimitUnit: str
    BalComSubLimitCode: str
    BalComSubLimitAmt: str
    BalComSubLimitUnit: str
    ComLimitCode: str
    ComLimitAmt: str
    ComLimitUnit: str
    BalComLimitCode: str
    BalComLimitAmt: str
    BalComLimitUnit: str


class ContractCondition(BaseModel):
    """Model for contract conditions from contract_conditions.csv."""
    
    MemberCode: str
    ConditionType: str
    ConditionApply: str
    EffFromDate: str
    EffToDate: str
    ConditionDetail: str
    Action: str
    SourceCondition: str
    Remarks: str
    CreateBy: str
    CreateDateTime: str
    ModifiedBy: str
    ModifiedDateTime: str
    
    @validator('EffFromDate', 'EffToDate')
    def validate_dates(cls, v, field):
        """Validate date fields."""
        if v:
            try:
                parse_date(v, field.name)
            except Exception:
                pass  # Keep original string
        return v
    
    @validator('CreateDateTime', 'ModifiedDateTime')
    def validate_datetimes(cls, v, field):
        """Validate datetime fields."""
        if v:
            try:
                parse_datetime(v, field.name)
            except Exception:
                pass  # Keep original string
        return v


class MemberCondition(BaseModel):
    """Model for member conditions from member_conditions.csv."""
    
    MemberCode: str
    ConditionType: str
    EffFromDate: str
    EffToDate: str
    ConditionDetail: str
    Action: str
    SourceCondition: str
    Remarks: str
    CreateBy: str
    CreateDateTime: str
    ModifiedBy: str
    ModifiedDateTime: str
    
    @validator('EffFromDate', 'EffToDate')
    def validate_dates(cls, v, field):
        """Validate date fields."""
        if v:
            try:
                parse_date(v, field.name)
            except Exception:
                pass  # Keep original string
        return v
    
    @validator('CreateDateTime', 'ModifiedDateTime')
    def validate_datetimes(cls, v, field):
        """Validate datetime fields."""
        if v:
            try:
                parse_datetime(v, field.name)
            except Exception:
                pass  # Keep original string
        return v


class ClaimRecord(BaseModel):
    """Model for claims from claims.csv."""
    
    ClaimNo: str
    MainBenefit: str
    ClaimSource: str
    ClaimType: str
    PatientNameTH: str
    PatientNameEN: str
    ClaimStatus: str
    CitizenID: str
    VisitDate: str
    DischargeDate: str
    AccidentDate: str
    IncurredAmt: str
    PayableAmt: str
    ContractNo: str
    InsurerCode: str
    InsurerTH: str
    InsurerEN: str
    CompanyCode: str
    CompanyTH: str
    CompanyEN: str
    CardType: str
    PolicyNo: str
    ProviderCode: str
    ProviderTH: str
    ProviderEN: str
    BatchNo: str
    DiagCode: str
    DiagTH: str
    DiagEN: str
    PaymentDate: str
    MemberCode: str
    
    @validator('VisitDate', 'DischargeDate', 'AccidentDate', 'PaymentDate')
    def validate_dates(cls, v, field):
        """Validate date fields."""
        if v:
            try:
                parse_date(v, field.name)
            except Exception:
                pass  # Keep original string
        return v
