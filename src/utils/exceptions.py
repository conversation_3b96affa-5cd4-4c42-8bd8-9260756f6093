"""
Custom exception classes for TPA API.
Provides specific exceptions for different error scenarios with proper HTTP status codes.
"""

from typing import Any, Dict, Optional
from fastapi import HTTPException, status


class TPAAPIException(Exception):
    """Base exception class for TPA API."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(TPAAPIException):
    """Exception raised for validation errors."""
    
    def __init__(self, message: str, field: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        self.field = field
        super().__init__(message, details)


class DataLoadingError(TPAAPIException):
    """Exception raised when CSV data loading fails."""
    
    def __init__(self, message: str, file_path: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        self.file_path = file_path
        super().__init__(message, details)


class MemberNotFoundError(TPAAPIException):
    """Exception raised when a member is not found."""
    
    def __init__(self, member_code: str, details: Optional[Dict[str, Any]] = None):
        message = f"Member not found: {member_code}"
        self.member_code = member_code
        super().__init__(message, details)


class InvalidParameterCombinationError(ValidationError):
    """Exception raised for invalid parameter combinations."""
    
    def __init__(self, provided_params: Dict[str, Any], details: Optional[Dict[str, Any]] = None):
        self.provided_params = provided_params
        message = f"Invalid parameter combination: {list(provided_params.keys())}"
        super().__init__(message, details=details)


class DateParsingError(ValidationError):
    """Exception raised when date parsing fails."""
    
    def __init__(self, date_value: str, field: str, details: Optional[Dict[str, Any]] = None):
        self.date_value = date_value
        message = f"Invalid date format in field '{field}': {date_value}"
        super().__init__(message, field=field, details=details)


# HTTP Exception converters
def validation_error_to_http(error: ValidationError) -> HTTPException:
    """Convert ValidationError to HTTPException."""
    return HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail={
            "error": "Validation Error",
            "message": error.message,
            "field": error.field,
            "details": error.details
        }
    )


def member_not_found_to_http(error: MemberNotFoundError) -> HTTPException:
    """Convert MemberNotFoundError to HTTPException."""
    return HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail={
            "error": "Member Not Found",
            "message": error.message,
            "member_code": error.member_code,
            "details": error.details
        }
    )


def data_loading_error_to_http(error: DataLoadingError) -> HTTPException:
    """Convert DataLoadingError to HTTPException."""
    return HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail={
            "error": "Data Loading Error",
            "message": error.message,
            "file_path": error.file_path,
            "details": error.details
        }
    )


def tpa_exception_to_http(error: TPAAPIException) -> HTTPException:
    """Convert generic TPAAPIException to HTTPException."""
    return HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail={
            "error": "Internal Server Error",
            "message": error.message,
            "details": error.details
        }
    )


# Exception handler mapping
EXCEPTION_HANDLERS = {
    ValidationError: validation_error_to_http,
    InvalidParameterCombinationError: validation_error_to_http,
    DateParsingError: validation_error_to_http,
    MemberNotFoundError: member_not_found_to_http,
    DataLoadingError: data_loading_error_to_http,
    TPAAPIException: tpa_exception_to_http,
}
