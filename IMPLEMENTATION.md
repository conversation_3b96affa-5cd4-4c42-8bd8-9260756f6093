# TPA API Implementation Documentation

## Project Overview

This document tracks the development progress of the TPA API, a FastAPI-based REST API for insurance policy and claims management. The API provides three endpoints (PolicyListSF, PolicyDetailSF, ClaimListSF) using comprehensive mock data stored in CSV format.

## Requirements Clarifications

Based on stakeholder feedback, the following clarifications were established:

- **Name Matching**: Exact match comparison (case-sensitive) for NAME_TH and NAME_EN parameters
- **Date/Time Handling**: Thailand timezone (UTC+7) for all date comparisons when filtering active policies
- **Response Ordering**: Natural order (as loaded from CSV) - no specific sorting required
- **Claim Status Correction**: "Authoried" corrected to "Authorized"
- **Empty Results**: Return empty list `[]` when no data found, not error messages

## Complete Implementation Plan

### Phase 1: Foundation ✅ COMPLETED
**Complexity**: Low | **Duration**: 2 hours | **Dependencies**: None

**Files Implemented**:
1. `config/settings.py` - Application configuration management
2. `src/utils/logger.py` - Structured logging setup
3. `src/utils/exceptions.py` - Custom exception classes
4. `src/utils/helpers.py` - Utility functions for date handling and data processing
5. `src/models/data_models.py` - Pydantic models for internal data structures

### Phase 2: Data Layer 🔄 NEXT
**Complexity**: Medium | **Duration**: 4 hours | **Dependencies**: Phase 1

**Files to Implement**:
1. `src/data/mock_data.py` - CSV data loading and initialization
2. `src/data/policy_data.py` - Policy data management and indexing
3. `src/data/claim_data.py` - Claim data management and indexing

### Phase 3: API Models
**Complexity**: Medium | **Duration**: 3 hours | **Dependencies**: Phase 1, 2

**Files to Implement**:
1. `src/models/request_models.py` - Pydantic request models with validation
2. `src/models/response_models.py` - Pydantic response models for JSON output

### Phase 4: Business Logic
**Complexity**: High | **Duration**: 8 hours | **Dependencies**: Phase 1, 2, 3

**Files to Implement**:
1. `src/services/validation_service.py` - Parameter validation logic
2. `src/services/policy_service.py` - Business logic for policies
3. `src/services/claim_service.py` - Business logic for claims

### Phase 5: API Layer
**Complexity**: Medium | **Duration**: 4 hours | **Dependencies**: Phase 1-4

**Files to Implement**:
1. `src/api/dependencies.py` - Shared dependencies and validation
2. `src/api/routes/policy_list.py` - PolicyListSF endpoint
3. `src/api/routes/policy_detail.py` - PolicyDetailSF endpoint
4. `src/api/routes/claim_list.py` - ClaimListSF endpoint

### Phase 6: Application Assembly
**Complexity**: Low | **Duration**: 2 hours | **Dependencies**: Phase 1-5

**Files to Implement**:
1. `src/main.py` - FastAPI application factory and startup
2. `docker/Dockerfile` - Container definition
3. `docker/docker-compose.yml` - Local development setup

**Total Estimated Duration**: 23 hours

## Phase 1 Implementation Summary ✅

### 1. Configuration Management (`config/settings.py`)

**Key Features**:
- Pydantic-based settings with environment variable support
- Thailand timezone configuration (UTC+7)
- Configurable logging, server, and API settings
- Type-safe configuration with field descriptions

**Technical Highlights**:
```python
# Thailand timezone support
THAILAND_TZ = timezone(timedelta(hours=settings.timezone_offset))

# Environment-based configuration
class Settings(BaseSettings):
    timezone_offset: int = Field(default=7, description="Thailand timezone offset (UTC+7)")
```

### 2. Structured Logging (`src/utils/logger.py`)

**Key Features**:
- JSON and text logging formatters
- Structured logging with extra fields support
- Uvicorn and FastAPI logger integration
- Configurable log levels and formats

**Technical Highlights**:
```python
# Custom JSON formatter for structured logging
class JSONFormatter(logging.Formatter):
    def format(self, record: logging.LogRecord) -> str:
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "message": record.getMessage(),
            # ... additional fields
        }
```

### 3. Exception Handling (`src/utils/exceptions.py`)

**Key Features**:
- Custom exception hierarchy with TPAAPIException base
- HTTP status code mapping for FastAPI integration
- Detailed error context with field names and details
- Specific exceptions for validation, data loading, and member lookup

**Technical Highlights**:
```python
# Exception to HTTP converter mapping
EXCEPTION_HANDLERS = {
    ValidationError: validation_error_to_http,
    MemberNotFoundError: member_not_found_to_http,
    DataLoadingError: data_loading_error_to_http,
}
```

### 4. Utility Functions (`src/utils/helpers.py`)

**Key Features**:
- Thailand timezone date parsing and validation
- Exact case-sensitive string matching (per requirements)
- Policy active status validation
- Claim status validation with "Authorized" correction
- Data indexing utilities for fast lookups

**Technical Highlights**:
```python
# Thailand timezone date handling
def get_current_date_thailand() -> date:
    thailand_tz = get_thailand_timezone()
    return datetime.now(thailand_tz).date()

# Exact string matching (case-sensitive)
def exact_match(value1: str, value2: str) -> bool:
    return normalize_string(value1) == normalize_string(value2)

# Claim status validation with correction
def validate_claim_status(status: str) -> bool:
    allowed_statuses = {
        "Approved", "Authorized", "Open", "Paid",
        "Pending", "Pending For Approval", "Rejected"
    }
    return status in allowed_statuses
```

### 5. Data Models (`src/models/data_models.py`)

**Key Features**:
- Complete Pydantic models for all 7 CSV data structures
- Type-safe field definitions matching CSV columns exactly
- Date and datetime validation with proper error handling
- Models for all data types: policies, payments, benefits, conditions, claims

**Technical Highlights**:
```python
# Type-safe models with validation
class PolicyRecord(BaseModel):
    MemberCode: str
    CitizenID: str
    PlanEffFrom: str
    PlanEffTo: str
    # ... 34 more fields

    @validator('PlanEffFrom', 'PlanEffTo')
    def validate_dates(cls, v, field):
        if v:
            try:
                parse_date(v, field.name)
            except Exception:
                pass  # Keep original string
        return v
```

## Technical Decisions and Rationale

### 1. **Pydantic for Configuration and Data Models**
- **Decision**: Use Pydantic for settings and data validation
- **Rationale**: Type safety, automatic validation, environment variable support
- **Benefit**: Catches configuration and data errors early

### 2. **Thailand Timezone Handling**
- **Decision**: Centralized timezone management with UTC+7
- **Rationale**: Requirements specify Thailand timezone for date comparisons
- **Implementation**: `get_thailand_timezone()` function used throughout

### 3. **Structured Logging**
- **Decision**: JSON and text formatters with configurable output
- **Rationale**: Production deployments need structured logs for monitoring
- **Benefit**: Easy integration with log aggregation systems

### 4. **Custom Exception Hierarchy**
- **Decision**: Specific exceptions with HTTP status code mapping
- **Rationale**: Better error handling and API response consistency
- **Benefit**: Clear error messages and proper HTTP status codes

### 5. **Exact String Matching**
- **Decision**: Case-sensitive exact matching for names
- **Rationale**: Stakeholder requirement for precise name searches
- **Implementation**: `exact_match()` function with string normalization

## Mock Data Structure Overview

The API uses 7 CSV files with comprehensive insurance data:

- **policies.csv** (12 records): Complete member and policy information
- **payment_details.csv** (17 records): Payment methods and bank details
- **policy_details.csv** (12 records): Main benefit information
- **benefits.csv** (12 records): Detailed benefit breakdown with limits
- **contract_conditions.csv** (11 records): Contract-level conditions
- **member_conditions.csv** (11 records): Member-specific conditions
- **claims.csv** (13 records): Claims history with all required statuses

**Data Relationships**:
- **MemberCode**: Primary key linking all data
- **PolicyNo**: Groups members under same policy
- **CitizenID**: Unique identification
- **InsurerCode/CompanyCode**: Organizational grouping

## API Endpoint Specifications

### 1. PolicyListSF
- **Path**: `/api/PolicyListSF`
- **Method**: GET
- **Parameters**: 9 different parameter combinations with INSURER_CODE + additional fields
- **Response**: List of policies with payment details
- **Filtering**: Active policies only (PlanEffFrom <= today <= PlanEffTo)

### 2. PolicyDetailSF
- **Path**: `/api/PolicyDetailSF`
- **Method**: GET
- **Parameters**: MEMBER_CODE (required)
- **Response**: Detailed policy information with benefits, conditions, claim history
- **Data Sources**: Multiple CSV files aggregated

### 3. ClaimListSF
- **Path**: `/api/ClaimListSF`
- **Method**: GET
- **Parameters**: MEMBER_CODE or (INSURER_CODE + CITIZEN_ID)
- **Response**: List of claims with allowed statuses only
- **Filtering**: Approved, Authorized, Open, Paid, Pending, Pending For Approval, Rejected

## Current Status

### ✅ Completed
- **Phase 1: Foundation** - All 5 components implemented and tested
- **Configuration Management** - Environment-based settings with Thailand timezone
- **Logging Infrastructure** - Structured JSON/text logging ready
- **Exception Framework** - Custom exceptions with HTTP mapping
- **Utility Functions** - Date handling, string matching, data processing
- **Data Models** - Type-safe Pydantic models for all CSV structures

### 🔄 Current State
- **Ready for Phase 2: Data Layer**
- **Dependencies Satisfied**: All foundation components in place
- **Next Steps**: Implement CSV loading and data indexing

### 📋 Remaining Work
- **Phase 2**: Data Layer (CSV loading, indexing)
- **Phase 3**: API Models (request/response models)
- **Phase 4**: Business Logic (validation, policy/claim services)
- **Phase 5**: API Layer (routes, dependencies)
- **Phase 6**: Application Assembly (main app, Docker)

## Integration Points

The Phase 1 components provide these integration points for subsequent phases:

1. **Settings**: `get_settings()` and `get_thailand_timezone()` for configuration
2. **Logging**: `get_logger()` for structured logging throughout application
3. **Exceptions**: Exception classes and HTTP converters for error handling
4. **Helpers**: Date parsing, string matching, and data processing utilities
5. **Models**: Type-safe data structures for CSV data validation

## Next Phase Readiness

Phase 2: Data Layer is ready to begin with:
- ✅ Configuration management for CSV file paths
- ✅ Logging infrastructure for data loading operations
- ✅ Exception handling for data loading errors
- ✅ Date parsing utilities for effective date filtering
- ✅ Data models for type-safe CSV parsing
- ✅ Helper functions for indexing and data processing

The foundation provides a robust base for implementing the data loading and indexing components required for the API endpoints.

## Development Notes

### Code Quality Standards
- **Type Hints**: All functions and classes use proper type annotations
- **Documentation**: Comprehensive docstrings for all modules and functions
- **Error Handling**: Specific exceptions with detailed error context
- **Testing Ready**: Components designed for easy unit testing
- **Production Ready**: Structured logging and configuration management

### Performance Considerations
- **In-Memory Storage**: Fast data access with pre-built indexes
- **Lazy Loading**: Data loaded once at startup
- **Efficient Lookups**: O(1) dictionary lookups for key-based searches
- **Memory Trade-offs**: Multiple indexes for query performance

### Security Considerations
- **Input Validation**: Pydantic models validate all input data
- **No Authentication**: As per requirements, APIs are open
- **Error Information**: Detailed errors for debugging, not security risks
- **Data Sanitization**: String normalization and safe dictionary access

This implementation provides a solid foundation for building a production-ready TPA API with proper error handling, logging, and data management capabilities.