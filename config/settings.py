"""
Application configuration management for TPA API.
Handles environment variables, application settings, and configuration validation.
"""

import os
from typing import Optional
from pydantic import BaseSettings, Field
from datetime import timezone, timedelta


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application settings
    app_name: str = Field(default="TPA API", description="Application name")
    app_version: str = Field(default="1.0.0", description="Application version")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Server settings
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8000, description="Server port")
    workers: int = Field(default=1, description="Number of worker processes")
    
    # API settings
    api_prefix: str = Field(default="/api", description="API prefix path")
    cors_origins: list[str] = Field(default=["*"], description="CORS allowed origins")
    
    # Data settings
    csv_data_path: str = Field(default="data/csv", description="Path to CSV data files")
    timezone_offset: int = Field(default=7, description="Thailand timezone offset (UTC+7)")
    
    # Logging settings
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Log format: json or text")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()

# Thailand timezone
THAILAND_TZ = timezone(timedelta(hours=settings.timezone_offset))


def get_settings() -> Settings:
    """Get application settings instance."""
    return settings


def get_thailand_timezone() -> timezone:
    """Get Thailand timezone object."""
    return THAILAND_TZ
