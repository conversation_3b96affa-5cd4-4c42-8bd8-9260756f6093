["tests/test_claim_data.py::TestClaimDataManager::test_build_indexes", "tests/test_claim_data.py::TestClaimDataManager::test_get_all_conditions_by_member_code", "tests/test_claim_data.py::TestClaimDataManager::test_get_claim_manager_singleton", "tests/test_claim_data.py::TestClaimDataManager::test_get_claims_by_citizen_id", "tests/test_claim_data.py::TestClaimDataManager::test_get_claims_by_insurer_and_citizen", "tests/test_claim_data.py::TestClaimDataManager::test_get_claims_by_member_code", "tests/test_claim_data.py::TestClaimDataManager::test_get_contract_conditions_by_member_code", "tests/test_claim_data.py::TestClaimDataManager::test_get_member_conditions_by_member_code", "tests/test_claim_data.py::TestClaimDataManager::test_get_valid_claim_statuses", "tests/test_claim_data.py::TestClaimDataManager::test_init", "tests/test_claim_data.py::TestClaimDataManager::test_validate_claim_status", "tests/test_mock_data.py::TestMockDataLoader::test_get_data_loader_singleton", "tests/test_mock_data.py::TestMockDataLoader::test_get_methods_ensure_loaded", "tests/test_mock_data.py::TestMockDataLoader::test_init", "tests/test_mock_data.py::TestMockDataLoader::test_initialize_data", "tests/test_mock_data.py::TestMockDataLoader::test_load_all_data_already_loaded", "tests/test_mock_data.py::TestMockDataLoader::test_load_csv_file_not_found", "tests/test_mock_data.py::TestMockDataLoader::test_load_csv_file_parse_error", "tests/test_mock_data.py::TestMockDataLoader::test_load_csv_file_success", "tests/test_mock_data.py::TestMockDataLoaderIntegration::test_load_real_csv_files", "tests/test_phase2_integration.py::TestPhase2Integration::test_claim_manager_integration", "tests/test_phase2_integration.py::TestPhase2Integration::test_complete_data_loading_flow", "tests/test_phase2_integration.py::TestPhase2Integration::test_data_consistency", "tests/test_phase2_integration.py::TestPhase2Integration::test_error_handling", "tests/test_phase2_integration.py::TestPhase2Integration::test_filtering_functionality", "tests/test_phase2_integration.py::TestPhase2Integration::test_policy_manager_integration", "tests/test_policy_data.py::TestPolicyDataManager::test_build_indexes", "tests/test_policy_data.py::TestPolicyDataManager::test_filter_policies_by_criteria", "tests/test_policy_data.py::TestPolicyDataManager::test_get_active_policies_by_insurer_code", "tests/test_policy_data.py::TestPolicyDataManager::test_get_payment_details_by_member_code", "tests/test_policy_data.py::TestPolicyDataManager::test_get_policy_by_citizen_id", "tests/test_policy_data.py::TestPolicyDataManager::test_get_policy_by_member_code", "tests/test_policy_data.py::TestPolicyDataManager::test_get_policy_manager_singleton", "tests/test_policy_data.py::TestPolicyDataManager::test_init"]